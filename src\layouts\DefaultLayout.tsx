import React from 'react';
import { Flex } from '@chakra-ui/react';
// import { NavUser } from './layoutComponent/NavUser';
import { FooterUser } from './layoutComponent/FooterUser';

type ComponentProps = {
  children: React.ReactNode;
};

export const DefaultLayout = ({ children }: ComponentProps) => {
  return (
    <Flex flexDirection="column" w={'100dvw'} position={'relative'}>
      {children}
      <FooterUser />
    </Flex>
  );
};
