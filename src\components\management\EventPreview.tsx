import { Flex, Text } from "@chakra-ui/react";
import React from "react";

export const EventPreview = () => {
  return (
    <Flex
      flexDirection={"column"}
      justifyContent={"center"}
      bg={"primaryPalette.50"}
      textAlign={"center"}
      py={".8rem"}
      gap={".3rem"}
      borderRadius={"1rem"}
    >
      <Text color={"primary"} fontWeight={"semibold"} fontSize={"1rem"}>
        2025.07.05 〜 2025.07.07
      </Text>
      <Text color={"primary"} fontWeight={"semibold"} fontSize={".9rem"}>
        [2泊を選択しました]
      </Text>
    </Flex>
  );
};
