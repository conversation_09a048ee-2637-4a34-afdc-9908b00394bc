import { CalendarComponent } from "@/components/management/CalendarComponent";
import { GoogleCalendarCard } from "@/components/management/GoogleCalendarCard";
import { Button, Flex, Select } from "@chakra-ui/react";
import React from "react";
import { useRouter } from "next/router";

const Index = () => {
  const router = useRouter();
  return (
    <Flex flexDirection={"column"} gap={"1rem"}>
      <Select placeholder="好立地、清潔なレジデンシャルステージ" size="lg">
        <option value="option1">
          好立地、清潔なレジデンシャルステージ, AsahiSta...
        </option>
        <option value="option2">
          好立地、清潔なレジデンシャルステージ, AsahiSta...
        </option>
        <option value="option3">
          好立地、清潔なレジデンシャルステージ, AsahiSta...
        </option>
      </Select>
      <GoogleCalendarCard disabled email="<EMAIL>" />
      <GoogleCalendarCard />

      <CalendarComponent />
      <Button
        variant={"primary"}
        fontWeight={"semibold"}
        onClick={() => {
          router.push("/user/management/1/123");
        }}
      >
        宿泊日程追加
      </Button>
    </Flex>
  );
};

export default Index;

Index.getInitialProps = async () => {
  return {
    layoutType: "user",
  };
};
