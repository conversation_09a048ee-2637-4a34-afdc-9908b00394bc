import { Flex, Grid, Image, Text, Button } from "@chakra-ui/react";
import React from "react";
import { useRouter } from "next/router";
import { TextPair } from "@/components/ui/TextPair";

const Index = () => {
  const router = useRouter();
  return (
    <Flex flexDirection={"column"}>
      <Grid
        gap={"1rem"}
        alignItems={"center"}
        pb={"1rem"}
        borderBottom={"1px solid"}
        borderColor={"gray.200"}
        gridTemplateColumns={"5rem 1fr"}
      >
        <Flex
          overflow={"hidden"}
          alignItems={"center"}
          justifyContent={"center"}
          objectFit={"cover"}
          borderRadius={"1rem"}
          w={"5rem"}
          h={"5rem"}
        >
          <Image
            src={"/images/Rectangle-1.jpg"}
            alt="Tokyo Guesthouse"
            w={"auto"}
            h={"5rem"}
          />
        </Flex>
        <Flex flexDirection={"column"} gap={".4rem"}>
          <Text fontWeight={"bold"} fontSize={"1.2rem"}>
            田中 太郎
          </Text>
          <Text fontSize={".9rem"} color={"gray.500"}>
            好立地、清潔なレジデンシャルステージ, AsahiStay...
          </Text>
        </Flex>
      </Grid>
      <Flex flexDirection={"column"} py={"1rem"}>
        <Text fontWeight={"bold"} fontSize={"1.1rem"}>
          予約情報
        </Text>
        <Flex flexDirection={"column"} py={"1rem"} gap={"1rem"}>
          <TextPair label={"予約番号"} value={"1234567890"} />
          <TextPair label={"日程"} value={"2025.07.05 - 2025.07.07"} />
          <TextPair label={"料金"} value={"¥123,000"} />
          <TextPair label={"最大宿泊人数"} value={"3名"} />
          <TextPair label={"チェックイン"} value={"15:00"} />
          <TextPair label={"チェックイアウト"} value={"12:00"} />
          <TextPair
            label={"要望事項"}
            value={
              "テキストテキストテキストテキストテキストテキストテキストテキスト"
            }
          />
        </Flex>
      </Flex>
      <Flex flexDirection={"column"} pt={"1rem"}>
        <Button
          variant={"primary"}
          bg={"gray.100"}
          color={"black"}
          fontWeight={"semibold"}
          onClick={() => {
            router.back();
          }}
        >
          戻る
        </Button>
      </Flex>
    </Flex>
  );
};

export default Index;

Index.getInitialProps = async () => {
  return {
    layoutType: "user",
  };
};
