import { extendTheme } from "@chakra-ui/react";
import { colors } from "./colors";
import { Buttons } from "./buttons";
import { texts } from "./Texts";
import { Inputs } from "./Inputs";
import { FormLabel } from "./formLabels";
import { Links } from "./links";

const breakpoints = {
  xs: "320px",
  sm: "480px",
  md: "768px",
  lg: "992px",
  xl: "1200px",
  "2xl": "1536px",
};
export const theme = extendTheme({
  breakpoints,
  fonts: {
    heading: "var(--font-pretendard)",
    body: "var(--font-pretendard)",
    mono: "var(--font-pretendard)",
  },
  styles: {
    global: {
      body: {
        fontFamily: "var(--font-pretendard)",
      },

      "h1, h2, h3, h4, h5, h6, p, span, a, button, input, textarea, select": {
        fontFamily: "var(--font-pretendard)",
      },
    },
  },
  colors,
  components: {
    Button: Buttons,
    Text: texts,
    Link: Links,
    Input: Inputs,
    FormLabel: FormLabel,
  },
});

export default theme;
