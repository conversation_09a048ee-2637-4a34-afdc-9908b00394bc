import { CalendarDayReselect } from "@/components/management/CalendarDayReselect";
import { CompleteModal } from "@/components/management/CompleteModal";
import { EventPreview } from "@/components/management/EventPreview";
import { Button, Flex, Select, useDisclosure } from "@chakra-ui/react";
import React from "react";

const Index = () => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  return (
    <Flex flexDirection={"column"} gap={"1rem"}>
      <Select placeholder="好立地、清潔なレジデンシャルステージ" size="lg">
        <option value="option1">
          好立地、清潔なレジデンシャルステージ, AsahiSta...
        </option>
        <option value="option2">
          好立地、清潔なレジデンシャルステージ, AsahiSta...
        </option>
        <option value="option3">
          好立地、清潔なレジデンシャルステージ, AsahiSta...
        </option>
      </Select>

      <CalendarDayReselect />
      <EventPreview />
      <Button
        variant={"primary"}
        fontWeight={"semibold"}
        onClick={() => {
          onOpen();
        }}
      >
        追加する
      </Button>
      <CompleteModal isOpen={isOpen} onClose={onClose} />
    </Flex>
  );
};

export default Index;

Index.getInitialProps = async () => {
  return {
    layoutType: "user",
  };
};
