import React from 'react';
import { Flex, Image, Text } from '@chakra-ui/react';

export default function IncomingChatHead() {
  return (
    <Flex gap={'1rem'} py={'1rem'} px={'.8rem'}>
      <Flex>
        <Flex
          overflow={'hidden'}
          alignItems={'center'}
          justifyContent={'center'}
          objectFit={'cover'}
          borderRadius={'1rem'}
          w={'3rem'}
          h={'3rem'}
        >
          <Image src={'/images/Rectangle-1.jpg'} alt="Tokyo Guesthouse" w={'auto'} h={'5rem'} />
        </Flex>
      </Flex>
      <Flex
        flexDirection={'column'}
        ml={'1rem'}
        gap={'.4rem'}
        bg={'chatBackground'}
        p={'1rem'}
        borderRadius={'xl'}
        position={'relative'}
        zIndex={2}
      >
        <Image
          position={'absolute'}
          src={'/images/Polygon-1.jpg'}
          alt="Tokyo Guesthouse"
          w={'auto'}
          h={'1rem'}
          top={'1rem'}
          left={'-1rem'}
          zIndex={1}
        />
        <Text fontWeight={'bold'} fontSize={'1.2rem'}>
          田中 太郎
        </Text>
        <Text>
          Hey! Did you finish the Hi-FI wireframes for flora app design?Hey! Did you finish the
          Hi-FI wireframes for flora app design?Hey! Did you finish the Hi-FI wireframes for flora
          app design?
        </Text>
      </Flex>
    </Flex>
  );
}
