import React from "react";
import { Flex } from "@chakra-ui/react";
import { HeaderUser } from "./layoutComponent/HeaderUser";
import { NavUser } from "./layoutComponent/NavUser";
import { FooterUser } from "./layoutComponent/FooterUser";

type ComponentProps = {
  children: React.ReactNode;
};

export const UserLayout = ({ children }: ComponentProps) => {
  return (
    <Flex flexDirection="column" alignItems={"center"} pt={"10rem"}>
      <HeaderUser />
      <NavUser />
      <Flex bg={"white"} flexDirection="column" w={"100dvw"} p={"1.5rem"}>
        {children}
      </Flex>
      <FooterUser />
    </Flex>
  );
};
