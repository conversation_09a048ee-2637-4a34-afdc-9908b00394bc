import React from "react";
import { Flex } from "@chakra-ui/react";
import { Message } from "@/components/message/Message";
import { messages } from "@/tempData/messages";

const Index = () => {
  return (
    <Flex flexDirection={"column"}>
      {messages.map((item) => (
        <Message key={item.id} message={item} />
      ))}
    </Flex>
  );
};

export default Index;

Index.getInitialProps = async () => {
  return {
    layoutType: "user",
  };
};
