import React from "react";
import { Grid, Text } from "@chakra-ui/react";

type TextPairProps = {
  label: string;
  value: string;
};

export const TextPair = ({ label, value }: TextPairProps) => {
  return (
    <Grid templateColumns={"6.5rem 1fr"} gap={"1rem"}>
      <Text fontSize={".9rem"} fontWeight={"bold"}>
        {label}
      </Text>
      <Text fontSize={".9rem"}>{value}</Text>
    </Grid>
  );
};
