import React from 'react';

interface IconProps {
  width?: number;
  height?: number;
  color?: string;
  className?: string;
}

const SendIcon: React.FC<IconProps> = ({ width = 24, height = 24, color = 'black', className }) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M20.3083 12.7956L4.05605 19.6276C3.77022 19.7436 3.4998 19.7208 3.2448 19.5591C2.98997 19.3975 2.86255 19.1654 2.86255 18.8629V5.13687C2.86255 4.83437 2.98997 4.60228 3.2448 4.44062C3.4998 4.27895 3.77022 4.25412 4.05605 4.36612L20.3083 11.2041C20.6521 11.3535 20.824 11.6187 20.824 11.9999C20.824 12.381 20.6521 12.6463 20.3083 12.7956ZM4.50005 17.6091L17.9325 11.9999L4.50005 6.31562V10.3841L10.6158 11.9999L4.50005 13.5656V17.6091Z"
        fill={color}
      ></path>
    </svg>
  );
};

export default SendIcon;
