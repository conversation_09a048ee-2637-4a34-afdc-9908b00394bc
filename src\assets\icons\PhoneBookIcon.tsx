import React from 'react';

interface IconProps {
  width?: number;
  height?: number;
  color?: string;
  className?: string;
}

const PhoneBookIcon: React.FC<IconProps> = ({
  width = 25,
  height = 25,
  color = 'black',
  className,
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 25 26"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M4.70325 19.4345C5.70325 18.5172 6.82675 17.777 8.07375 17.2138C9.32075 16.6508 10.6785 16.3693 12.147 16.3693C13.6153 16.3693 14.9738 16.6489 16.2225 17.208C17.4713 17.767 18.5957 18.5052 19.5957 19.4225V5.55678H4.70325V19.4345ZM12.1988 14.5678C13.1779 14.5678 14.0038 14.2317 14.6765 13.5595C15.349 12.8874 15.6853 12.0617 15.6853 11.0825C15.6853 10.1034 15.3493 9.27853 14.6773 8.60803C14.0051 7.93737 13.1794 7.60203 12.2003 7.60203C11.2211 7.60203 10.3952 7.93712 9.7225 8.60728C9.05 9.27745 8.71375 10.102 8.71375 11.081C8.71375 12.0602 9.04975 12.8861 9.72175 13.5588C10.3939 14.2315 11.2196 14.5678 12.1988 14.5678ZM4.70325 22.1525C4.24325 22.1525 3.84442 21.9836 3.50675 21.6458C3.16892 21.3081 3 20.9093 3 20.4493V5.55678C3 5.09512 3.16892 4.69487 3.50675 4.35603C3.84442 4.01703 4.24325 3.84753 4.70325 3.84753H19.5957C20.0574 3.84753 20.4577 4.01703 20.7965 4.35603C21.1355 4.69487 21.305 5.09512 21.305 5.55678V20.4493C21.305 20.9093 21.1355 21.3081 20.7965 21.6458C20.4577 21.9836 20.0574 22.1525 19.5957 22.1525H4.70325ZM5.79025 20.4493H18.4957C17.4664 19.5739 16.4305 18.937 15.388 18.5385C14.3457 18.1402 13.2662 17.941 12.1495 17.941C11.0328 17.941 9.95533 18.1402 8.917 18.5385C7.8785 18.937 6.83625 19.5739 5.79025 20.4493ZM12.1975 13.002C11.6652 13.002 11.2134 12.8153 10.8423 12.4418C10.4709 12.0684 10.2853 11.615 10.2853 11.0815C10.2853 10.548 10.4716 10.0957 10.8443 9.72453C11.2169 9.35337 11.6693 9.16778 12.2015 9.16778C12.7338 9.16778 13.1856 9.35403 13.5567 9.72653C13.9281 10.0992 14.1138 10.5517 14.1138 11.084C14.1138 11.6162 13.9274 12.069 13.5548 12.4423C13.1821 12.8155 12.7297 13.002 12.1975 13.002Z"
        fill={color}
      ></path>
    </svg>
  );
};

export default PhoneBookIcon;
