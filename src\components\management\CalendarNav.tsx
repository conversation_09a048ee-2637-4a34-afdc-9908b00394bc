import ArrowRight from "@/assets/icons/ArrowRight";
import { Button, Flex, Text } from "@chakra-ui/react";
import React from "react";

export const CalendarNav = () => {
  return (
    <Flex
      gap={"1rem"}
      alignItems={"center"}
      justifyContent={"center"}
      h={"4rem"}
    >
      <Button fontWeight={"bold"}>{"<"}</Button>
      <Text>2025年 07月</Text>
      <Button
        bg={"primary"}
        color={"white"}
        borderRadius={"full"}
        p={0}
        w={"2.5rem"}
        h={"2.5rem"}
      >
        <ArrowRight width={20} height={20} color={"white"} />
      </Button>
    </Flex>
  );
};
