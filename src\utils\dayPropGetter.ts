type props = {
  events: any[];
  date: Date;
};

export const dayPropGetter = ({ events, date }: props) => {
  const dayStart = new Date(date.setHours(0, 0, 0, 0));

  const blackoutEvent = events.find((event) => {
    if (!event.data?.blackout) return false;
    return dayStart >= new Date(event.start) && dayStart <= new Date(event.end);
  });

  const appointmentEvent = events.find((event) => {
    if (!event.data?.event) return false;
    return dayStart >= new Date(event.start) && dayStart <= new Date(event.end);
  });

  const checkedInEvent = events.find((event) => {
    // if (!event.data?.checkedin) return false;
    return dayStart >= new Date(event.start) && dayStart <= new Date(event.end);
  });

  if (blackoutEvent) {
    return {
      style: {
        backgroundColor: "#FAE8E6", // light red background
        color: "#D74132",
        textDecoration: "line-through",
        textDecorationThickness: "2px",
      },
    };
  }

  if (appointmentEvent) {
    return {
      style: {
        backgroundColor: "#E6F0FA", // light blue background
        color: "#3174ad",
      },
    };
  }

  if (checkedInEvent) {
    return {
      style: {
        backgroundColor: "#E6FAE6", // light green background
        color: "#2D7A2D",
        fontWeight: "bold",
      },
    };
  }

  return {};
};
