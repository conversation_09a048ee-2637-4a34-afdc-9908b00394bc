import "@/styles/globals.css";
import "@/styles/fonts.css";
import type { AppProps } from "next/app";
import { ChakraProvider } from "@chakra-ui/react";
import { Layout } from "@/layouts/Layout";
import Head from "next/head";
import { theme } from "@/theme";

type PageProps = {
  layoutType?: "blank" | "admin" | "user";
};

export default function App({ Component, pageProps }: AppProps<PageProps>) {
  const layoutType = pageProps.layoutType || "blank";
  return (
    <>
      <Head>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>
      <ChakraProvider theme={theme}>
        <Layout type={layoutType}>
          <Component {...pageProps} />
        </Layout>
      </ChakraProvider>
    </>
  );
}
