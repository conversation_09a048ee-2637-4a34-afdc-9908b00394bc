import React from "react";

interface IconProps {
  width?: number;
  height?: number;
  color?: string;
  className?: string;
}

const CircleCheck: React.FC<IconProps> = ({
  width = 72,
  height = 73,
  color = "#D74132",
  className,
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 72 73"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M27 36.8372L33 42.8372L45 30.8372M63 36.8372C63 51.7488 50.9117 63.8372 36 63.8372C21.0883 63.8372 9 51.7488 9 36.8372C9 21.9255 21.0883 9.83716 36 9.83716C50.9117 9.83716 63 21.9255 63 36.8372Z"
        stroke={color}
        strokeWidth="6"
        strokeLinecap="round"
        strokeLinejoin="round"
      ></path>
    </svg>
  );
};

export default CircleCheck;
