import React from "react";
import { Calendar, momentLocalizer } from "react-big-calendar";
import moment from "moment";
import "moment/locale/ja";
import { CalendarNav } from "./CalendarNav";
import "react-big-calendar/lib/css/react-big-calendar.css";
import { Button } from "@chakra-ui/react";
import { events } from "@/tempData/events";
import { useRouter } from "next/router";

export const CalendarDayReselect = () => {
  const router = useRouter();

  const planId = router.query["plan-id"];

  const selectedEvent = events.find((event) => {
    if (!event.data?.event) return false;
    return event.id && event.id.toString() === planId;
  });

  console.log(selectedEvent);

  moment.locale("ja");
  const localizer = momentLocalizer(moment);

  const CalendarComponent = Calendar as React.ComponentType<any>;
  const [date, setDate] = React.useState(new Date());

  const dayPropGetter = (date: Date) => {
    const dayStart = new Date(
      date.getFullYear(),
      date.getMonth(),
      date.getDate()
    );

    const hasBlackout = events.some((event) => {
      if (!event.data?.blackout) return false;

      const eventStart = new Date(event.start);
      const eventEnd = new Date(event.end);

      const normalizedStart = new Date(
        eventStart.getFullYear(),
        eventStart.getMonth(),
        eventStart.getDate()
      );
      const normalizedEnd = new Date(
        eventEnd.getFullYear(),
        eventEnd.getMonth(),
        eventEnd.getDate()
      );

      return dayStart >= normalizedStart && dayStart <= normalizedEnd;
    });

    const hasAppointment = events.some((event) => {
      // console.log(event);

      if (!event.data?.event) return false;
      if (event.id.toString() === planId) return false;

      const eventStart = new Date(event.start);
      const eventEnd = new Date(event.end);

      const normalizedStart = new Date(
        eventStart.getFullYear(),
        eventStart.getMonth(),
        eventStart.getDate()
      );
      const normalizedEnd = new Date(
        eventEnd.getFullYear(),
        eventEnd.getMonth(),
        eventEnd.getDate()
      );

      return dayStart >= normalizedStart && dayStart <= normalizedEnd;
    });

    const selectedEvent = events.some((event) => {
      if (!event.data?.event) return false;

      const eventStart = new Date(event.start);
      const eventEnd = new Date(event.end);

      const normalizedStart = new Date(
        eventStart.getFullYear(),
        eventStart.getMonth(),
        eventStart.getDate()
      );
      const normalizedEnd = new Date(
        eventEnd.getFullYear(),
        eventEnd.getMonth(),
        eventEnd.getDate()
      );
      return dayStart >= normalizedStart && dayStart <= normalizedEnd;
    });

    if (hasBlackout) {
      return {
        className: "blackout-date",
        style: {
          backgroundColor: "#eeeeee",
          color: "#D74132",
        },
      };
    }

    if (hasAppointment) {
      return {
        style: {
          backgroundColor: "#eeeeee",
          color: "#3174ad",
        },
      };
    }
    if (selectedEvent) {
      console.log(selectedEvent);

      return {
        style: {
          backgroundColor: "#ecaca6ff",
          color: "white",
        },
      };
    }

    return {};
  };

  const DateHeader: React.FC<any> = ({ date, label, onDrillDown }) => {
    const hasBlackout = events.some((e) => {
      if (!e.data?.blackout) return false;
      const start = new Date(e.start);
      start.setHours(0, 0, 0, 0);
      const end = new Date(e.end);
      end.setHours(23, 59, 59, 999);

      const d = new Date(date);
      d.setHours(12, 0, 0, 0);
      return d >= start && d <= end;
    });

    const hasEvent = events.some((e) => {
      if (!e.data?.event) return false;
      if (e.id.toString() === planId) return false;
      const start = new Date(e.start);
      start.setHours(0, 0, 0, 0);
      const end = new Date(e.end);
      end.setHours(23, 59, 59, 999);

      const d = new Date(date);
      d.setHours(12, 0, 0, 0);
      return d >= start && d <= end;
    });
    const selectedEvent = events.some((e) => {
      if (!e.data?.event) return false;
      if (e.id.toString() !== planId) return false;
      const start = new Date(e.start);
      start.setHours(0, 0, 0, 0);
      const end = new Date(e.end);
      end.setHours(23, 59, 59, 999);

      const d = new Date(date);
      d.setHours(12, 0, 0, 0);
      return d >= start && d <= end;
    });

    return (
      <Button
        bg={"transparent"}
        type="button"
        className="rbc-button-link"
        onClick={onDrillDown}
        fontSize={".8rem"}
        style={
          hasBlackout
            ? { textDecoration: "line-through", textDecorationThickness: "1px" }
            : hasEvent
            ? { textDecoration: "line-through", textDecorationThickness: "1px" }
            : undefined
        }
        color={selectedEvent ? "white" : "black"}
      >
        {label}
      </Button>
    );
  };
  return (
    <>
      <CalendarNav date={date} setDate={setDate} />
      <div>
        <CalendarComponent
          components={{
            month: {
              dateHeader: DateHeader,
            },
          }}
          localizer={localizer}
          events={[]}
          startAccessor="start"
          endAccessor="end"
          style={{ height: 500 }}
          culture="ja"
          toolbar={false}
          views={["month"]}
          date={date}
          onNavigate={setDate}
          dayPropGetter={dayPropGetter}
        />
      </div>
    </>
  );
};
