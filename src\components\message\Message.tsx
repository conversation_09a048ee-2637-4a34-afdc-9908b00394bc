import ClockIcon from "@/assets/icons/ClockIcon";
import { Flex, Grid, Image, Link, Text } from "@chakra-ui/react";
import React from "react";
import { MessagesType } from "@/types/MessagesType";
import { users } from "@/tempData/users";

export const Message = ({ message }: { message: MessagesType }) => {
  const user = users.find((item) => item.id === message.user);

  return (
    <Link href={`/user/message/${message.id}`} textDecoration={"none"}>
      <Grid
        templateColumns={"5rem 1fr 5rem"}
        borderBottom={"1px solid"}
        borderColor={"gray.200"}
        py={".8rem"}
        px={".5rem"}
        gap={"1rem"}
      >
        <Flex
          overflow={"hidden"}
          alignItems={"center"}
          justifyContent={"center"}
          objectFit={"cover"}
          borderRadius={"1rem"}
          w={"5rem"}
          h={"5rem"}
        >
          <Image
            src={user && user.image}
            alt="Tokyo Guesthouse"
            w={"auto"}
            h={"5rem"}
          />
        </Flex>
        <Flex flexDirection={"column"} gap={".4rem"}>
          <Text fontWeight={"bold"} fontSize={"1.2rem"}>
            {user && user.name}
          </Text>
          <Text fontSize={".7rem"} color={"gray.500"}>
            {user && user.address}
          </Text>
          <Text fontSize={".9rem"} noOfLines={1} w={"90%"} overflow={"hidden"}>
            {/* {chat && chat.content} */}
          </Text>
        </Flex>
        <Grid gridTemplateRows={"1fr 1fr"}>
          <Flex justifyContent={"flex-end"} alignItems={"center"} gap={".3rem"}>
            <ClockIcon />
            <Text fontSize={".9rem"} color={"gray.500"}>
              {/* {chat && chat.time} */}
            </Text>
          </Flex>
          <Flex justifyContent={"flex-end"} alignItems={"center"}>
            <Flex bg={"red"} borderRadius={"full"} p={".3rem"}></Flex>
          </Flex>
        </Grid>
      </Grid>
    </Link>
  );
};
