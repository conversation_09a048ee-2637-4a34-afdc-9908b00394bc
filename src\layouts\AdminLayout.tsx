import React from 'react';
import { Flex } from '@chakra-ui/react';
// import { Footer } from './layoutComponent/Footer';
// import { Navbar } from './layoutComponent/Navbar';

type ComponentProps = {
  children: React.ReactNode;
};

export const AdminLayout = ({ children }: ComponentProps) => {
  return (
    <Flex flexDirection="column">
      <Flex
        flexDirection="column"
        // alignItems={{ base: "center" }}
        px={{ base: '0', md: '5rem' }}
      >
        {/* <Navbar /> */}
        <Flex flexDirection="column" minH={'56dvh'}>
          {children}
        </Flex>
      </Flex>
      {/* <Footer /> */}
    </Flex>
  );
};
