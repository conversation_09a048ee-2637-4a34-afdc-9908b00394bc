import {
  Text,
  Image,
  Modal,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ModalOverlay,
  But<PERSON>,
} from "@chakra-ui/react";
import React from "react";
import CircleCheck from "@/assets/icons/CircleCheck";

type ComponentProp = {
  isOpen: boolean;
  onClose: () => void;
};

export const CompleteModal = ({ isOpen, onClose }: ComponentProp) => {
  return (
    <Modal isOpen={isOpen} onClose={onClose} isCentered size={"lg"}>
      <ModalOverlay />
      <ModalContent borderRadius={"1rem"} py={".5rem"}>
        <ModalHeader display={"flex"} justifyContent={"center"}>
          <Image
            src={"/images/Column.png"}
            alt="Tokyo Minbak"
            w={"auto"}
            h={"1.8rem"}
          />
        </ModalHeader>
        <ModalBody
          display={"flex"}
          justifyContent={"center"}
          alignItems={"center"}
          flexDirection={"column"}
        >
          <CircleCheck />
          <Text
            fontSize={"1.4rem"}
            fontWeight={"semibold"}
            color={"black"}
            mb={".8rem"}
          >
            2025.07.05 〜 2025.07.07
          </Text>
          <Text color={"black"}>以上の日程で予約が完了しました。</Text>
        </ModalBody>
        <ModalFooter>
          <Button
            w={"100%"}
            variant={"primary"}
            bg={"gray.100"}
            color={"black"}
            fontWeight={"semibold"}
            onClick={onClose}
          >
            閉じる
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};
