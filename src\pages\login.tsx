import {
  Button,
  Flex,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Image,
  Input,
  Text,
} from "@chakra-ui/react";
import React from "react";
import { useRouter } from "next/router";

const Login = () => {
  const router = useRouter();
  return (
    <Flex
      px={{ base: "1.5rem", md: "5rem" }}
      flexDirection={"column"}
      alignItems={"center"}
      pt={{ base: "5rem", md: "5rem" }}
      minH={"100dvh"}
    >
      <Flex
        w={"100%"}
        flexDirection={"column"}
        gap={"1rem"}
        justifyContent={"center"}
        alignItems={"center"}
      >
        <Flex
          flexDirection={"column"}
          gap={"1rem"}
          justifyContent={"center"}
          alignItems={"center"}
        >
          <Image
            src={"/images/Column.png"}
            alt="Tokyo Guesthouse"
            w={"20rem"}
            h={"auto"}
          />
          <Text
            fontSize={"1.5rem"}
            fontWeight={"semi-bold"}
            letterSpacing={".005em"}
          >
            Please log in to continue
          </Text>
        </Flex>
        <Flex w={"100%"} flexDirection={"column"} gap={"1rem"}>
          <FormControl>
            <FormLabel fontWeight={"normal"}>ID</FormLabel>
            <Input type="email" placeholder="" />
            <FormErrorMessage>Error</FormErrorMessage>
          </FormControl>
          <FormControl>
            <FormLabel fontWeight={"normal"}>Password</FormLabel>
            <Input type="password" placeholder="" />
            <FormErrorMessage>Error</FormErrorMessage>
          </FormControl>
          <Button
            variant={"primary"}
            mt={"1rem"}
            onClick={() => {
              router.push("/user/sales");
            }}
          >
            ログイン
          </Button>
        </Flex>
      </Flex>
    </Flex>
  );
};

export default Login;
