import ArrowRight from "@/assets/icons/ArrowRight";
import { Box, Flex, Grid, Text } from "@chakra-ui/react";
import React from "react";
import { useRouter } from "next/router";

export const ResPreview = () => {
  const router = useRouter();
  return (
    <Grid
      templateColumns={"1fr 2rem"}
      borderBottom={"1px solid"}
      borderColor={"gray.200"}
      py={"1rem"}
      onClick={() => {
        router.push("/user/management/1");
      }}
    >
      <Flex flexDirection={"column"} gap={".4rem"}>
        <Text
          noOfLines={2}
          overflow={"hidden"}
          w={"90%"}
          fontWeight={"semibold"}
          fontSize={"1.1rem"}
        >
          好立地、清潔なレジデンシャルステージ, AsahiStay -Shinjuku
          好立地、清潔なレジデンシャルステージ, AsahiStay -Shinjuku
          好立地、清潔なレジデンシャルステージ, Asahi<PERSON>tay -Shinjuku
          好立地、清潔なレジデンシャルステージ, AsahiStay -Shinjuku
        </Text>
        <Text
          noOfLines={1}
          overflow={"hidden"}
          w={"70%"}
          fontSize={".9rem"}
          color={"gray.400"}
        >
          東京都新宿区のまるまる貸切のアパート・マンション東京都新宿区のまるまる貸切のアパート・マンション東京都新宿区のまるまる貸切のアパート・マンション東京都新宿区のまるまる貸切のアパート・マンション
        </Text>
        <Flex mt={".5rem"} gap={"3rem"}>
          <Text>北海道</Text>
          <Flex gap={".3rem"} alignItems={"center"} w={"6rem"}>
            <Flex
              bg={"green.200"}
              borderRadius={"full"}
              h={".6rem"}
              w={".6rem"}
            ></Flex>
            <Text fontWeight={"semibold"}>公開</Text>
          </Flex>
        </Flex>
      </Flex>
      <Flex justifyContent={"flex-end"} alignItems={"center"}>
        <ArrowRight />
      </Flex>
    </Grid>
  );
};
