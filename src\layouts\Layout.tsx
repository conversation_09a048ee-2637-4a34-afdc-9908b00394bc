import { ReactNode } from 'react';
import { AdminLayout } from './AdminLayout';
import { UserLayout } from './UserLayout';
import { DefaultLayout } from './DefaultLayout';

type LayoutProps = {
  children: ReactNode;
  type: 'blank' | 'admin' | 'user';
  navConfig?: any;
  activePage?: string;
  setActivePage?: (page: string) => void;
};

export const Layout = ({ children, type, navConfig, activePage, setActivePage }: LayoutProps) => {
  if (type === 'admin') {
    return <AdminLayout>{children}</AdminLayout>;
  }

  if (type === 'user') {
    return <UserLayout>{children}</UserLayout>;
  }

  return <DefaultLayout>{children}</DefaultLayout>;
};
