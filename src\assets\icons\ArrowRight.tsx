import React from "react";

interface IconProps {
  width?: number;
  height?: number;
  color?: string;
  className?: string;
}

const ArrowRight: React.FC<IconProps> = ({
  width = 24,
  height = 24,
  color = "#333333",
  className,
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M9 18.0261L14.7 12.9786C14.7942 12.8978 14.8694 12.7993 14.921 12.6892C14.9725 12.5792 14.9991 12.4602 14.9991 12.3398C14.9991 12.2195 14.9725 12.1004 14.921 11.9904C14.8694 11.8804 14.7942 11.7818 14.7 11.7011L9 6.65356"
        stroke={color}
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      ></path>
    </svg>
  );
};

export default ArrowRight;
