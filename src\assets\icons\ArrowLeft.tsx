import React from "react";

interface IconProps {
  width?: number;
  height?: number;
  color?: string;
  className?: string;
}

const ArrowLeft: React.FC<IconProps> = ({
  width = 24,
  height = 24,
  color = "#333333",
  className,
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <mask
        id="mask0_4076_34105"
        style={{ maskType: "alpha" }}
        maskUnits="userSpaceOnUse"
        x="0"
        y="0"
        width="24"
        height="24"
      >
        <rect width="24" height="24" fill="currentColor"></rect>
      </mask>
      <g mask="url(#mask0_4076_34105)">
        <path
          d="M10.8 12.0008L14.7 15.9008C14.8833 16.0841 14.975 16.3174 14.975 16.6008C14.975 16.8841 14.8833 17.1174 14.7 17.3008C14.5167 17.4841 14.2833 17.5758 14 17.5758C13.7167 17.5758 13.4833 17.4841 13.3 17.3008L8.69999 12.7008C8.59999 12.6008 8.52915 12.4924 8.48749 12.3758C8.44582 12.2591 8.42499 12.1341 8.42499 12.0008C8.42499 11.8674 8.44582 11.7424 8.48749 11.6258C8.52915 11.5091 8.59999 11.4008 8.69999 11.3008L13.3 6.70078C13.4833 6.51745 13.7167 6.42578 14 6.42578C14.2833 6.42578 14.5167 6.51745 14.7 6.70078C14.8833 6.88411 14.975 7.11745 14.975 7.40078C14.975 7.68411 14.8833 7.91745 14.7 8.10078L10.8 12.0008Z"
          fill="currentColor"
        ></path>
      </g>
    </svg>
  );
};

export default ArrowLeft;
