import React from 'react';
import { Flex, Text } from '@chakra-ui/react';
import { tempSales } from '@/tempData/sales/sales';

const getTotalSales = () => {
  return tempSales.reduce((acc, item) => acc + item.amount, 0);
};

const getEstemateSettlement = () => {
  return tempSales.reduce((acc, item) => acc + item.amount, 0) * 0.18;
};

export const Header = () => {
  const totalSales = getTotalSales();
  const estimateSettlement = getEstemateSettlement();

  return (
    <Flex
      flexDirection={'row'}
      justifyContent={'space-between'}
      pb={'1rem'}
      borderBottom={'1px solid'}
      borderColor={'gray.100'}
    >
      <Flex alignItems={'center'} gap={'1rem'}>
        <Text fontWeight={'600'} fontSize={'1.3rem'}>
          総予約数
        </Text>
        <Text fontWeight={'600'} fontSize={'1.2rem'}>
          {tempSales.length}件
        </Text>
      </Flex>
      <Flex w={'14rem'} flexDirection={'column'}>
        <Flex w={'100%'} justifyContent={'space-between'}>
          <Text fontWeight={'600'} fontSize={'1.2rem'}>
            予約総額
          </Text>
          <Text fontWeight={'400'} fontSize={'1.2rem'}>
            ¥ {totalSales.toLocaleString()}
          </Text>
        </Flex>
        <Flex w={'100%'} justifyContent={'space-between'}>
          <Text fontWeight={'600'} fontSize={'1.2rem'}>
            精算見込額
          </Text>
          <Text fontWeight={'400'} fontSize={'1.2rem'}>
            ¥ {estimateSettlement.toLocaleString()}
            {/* {tempSales[1].amount} */}
          </Text>
        </Flex>
      </Flex>
    </Flex>
  );
};
