import React from "react";
import { Calendar, momentLocalizer } from "react-big-calendar";
import moment from "moment";
import "moment/locale/ja";
import { CalendarNav } from "./CalendarNav";
import "react-big-calendar/lib/css/react-big-calendar.css";
import { Event } from "./customEvents/Event";
import { BlackoutEvent } from "./customEvents/BlackoutEvent";

export const CalendarComponent = () => {
  moment.locale("ja");
  const localizer = momentLocalizer(moment);

  const CalendarComponent = Calendar as React.ComponentType<any>;

  const events = [
    {
      id: 1,
      title: "予約確認",
      start: moment("2025-08-15T10:00:00").toDate(),
      end: moment("2025-08-15T11:00:00").toDate(),
    },
    {
      id: 2,
      title: "user name",
      start: moment("2025-08-16T15:00:00").toDate(),
      end: moment("2025-08-17T16:00:00").toDate(),
    },

    {
      id: 1,
      start: moment("2025-08-18T10:00:00").toDate(),
      end: moment("2025-08-22T11:00:00").toDate(),
      data: {
        event: {
          name: "予約確認",
        },
      },
    },
    {
      id: 2,
      start: moment("2025-08-25T15:00:00").toDate(),
      end: moment("2025-08-29T16:00:00").toDate(),
      data: {
        blackout: {
          name: "Blackout",
        },
      },
    },
  ];

  const [date, setDate] = React.useState(new Date());

  const components = {
    event: ({ event }: any) => {
      const data = event?.data;
      if (data?.event) {
        return <Event appointment={data.event} />;
      }
      if (data?.blackout) {
        return <BlackoutEvent appointment={data.blackout} />;
      }
      return null;
      // console.log(event);
    },
  };

  const dayPropGetter = (date: Date) => {
    const hasBlackout = events.some((event) => {
      const eventStart = new Date(event.start);
      const eventEnd = new Date(event.end);
      return event.data?.blackout && date >= eventStart && date <= eventEnd;
    });

    if (hasBlackout) {
      return {
        className: "blackout-date",
        style: {
          backgroundColor: "#FAE8E6",
          color: "#D74132",
          textDecoration: "line-through",
          textDecorationThickness: "2px",
        },
      };
    }

    return {};
  };

  return (
    <>
      <CalendarNav date={date} setDate={setDate} />
      <div>
        <CalendarComponent
          components={components}
          localizer={localizer}
          events={events}
          startAccessor="start"
          endAccessor="end"
          style={{ height: 500 }}
          culture="ja"
          toolbar={false}
          views={["month"]}
          date={date}
          onNavigate={setDate}
          dayPropGetter={dayPropGetter}
        />
      </div>
    </>
  );
};
