import React from 'react';
import { Flex, Text } from '@chakra-ui/react';

type SalesProps = {
  title: string;
  amount: number;
};

export const Sales = ({ title, amount }: SalesProps) => {
  const formatAmmount = (amount: string) => {
    return amount.replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,');
  };
  return (
    <Flex
      justifyContent={'space-between'}
      py={'1.2rem'}
      pl={'1rem'}
      borderBottom={'1px solid'}
      borderColor={'gray.100'}
    >
      <Text fontSize={'1.1rem'} fontWeight={'bold'} noOfLines={1} w={'60%'} overflow={'hidden'}>
        {title}
      </Text>
      <Text color={amount < 0 ? 'red.500' : 'secondary'} fontSize={'1rem'}>
        ¥ {amount.toLocaleString()}
      </Text>
    </Flex>
  );
};
