import {
  <PERSON><PERSON>,
  Image,
  <PERSON>u,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>con<PERSON><PERSON>on,
  <PERSON>,
} from "@chakra-ui/react";
import { ChevronDownIcon } from "@chakra-ui/icons";
import React from "react";

export const HeaderUser = () => {
  return (
    <Flex
      w={"100dvw"}
      justifyContent={"space-between"}
      px={{ base: "1.5rem", md: "5rem" }}
      py={"2rem"}
      position={"fixed"}
      top={"0"}
      bg={"white"}
      zIndex={100}
    >
      <Image
        src={"/images/Column.png"}
        alt="Tokyo Guesthouse"
        w={"auto"}
        h={"2rem"}
      />
      <Flex alignItems={"center"} gap={"1rem"}>
        <Menu>
          <Flex alignItems={"center"} gap={"1rem"}>
            <Flex
              overflow={"hidden"}
              alignItems={"center"}
              justifyContent={"center"}
              borderRadius={"full"}
              objectFit={"cover"}
              w={"1.8rem"}
              h={"1.8rem"}
            >
              <Image
                src={"/images/Rectangle-1.jpg"}
                alt="Tokyo Guesthouse"
                w={"auto"}
                h={"3rem"}
              />
            </Flex>
            <Text fontSize={"sm"} fontWeight={"bold"}>
              田中 太郎
            </Text>
          </Flex>
          <MenuButton
            as={IconButton}
            icon={<ChevronDownIcon />}
            variant="ghost"
            transition="all 0.2s"
            _expanded={{ transform: "rotate(180deg)" }}
          />
          <MenuList>
            <MenuItem gap={"1rem"}>
              <Image
                src={"/images/Log-out.svg"}
                alt="Tokyo Guesthouse"
                w={"auto"}
                h={"1rem"}
              />
              ログアウト
            </MenuItem>
          </MenuList>
        </Menu>
      </Flex>
    </Flex>
  );
};
