import React from 'react';

interface IconProps {
  width?: number;
  height?: number;
  color?: string;
  className?: string;
}

const ClockIcon: React.FC<IconProps> = ({
  width = 14,
  height = 14,
  color = '#AFB8CF',
  className,
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 14 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M6.66667 12.3334C9.6 12.3334 12 9.93337 12 7.00004C12 4.06671 9.6 1.66671 6.66667 1.66671C3.73333 1.66671 1.33333 4.06671 1.33333 7.00004C1.33333 9.93337 3.73333 12.3334 6.66667 12.3334ZM6.66667 0.333374C10.3333 0.333374 13.3333 3.33337 13.3333 7.00004C13.3333 10.6667 10.3333 13.6667 6.66667 13.6667C3 13.6667 0 10.6667 0 7.00004C0 3.33337 3 0.333374 6.66667 0.333374ZM10 6.66671V7.66671H6V3.66671H7V6.66671H10Z"
        fill={color}
      ></path>
    </svg>
  );
};

export default ClockIcon;
