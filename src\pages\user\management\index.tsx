import { ResPreview } from "@/components/management/ResPreview";
import { Flex } from "@chakra-ui/react";
import React from "react";

const Index = () => {
  return (
    <Flex flexDirection={"column"} px={".5rem"}>
      <ResPreview />
      <ResPreview />
      <ResPreview />
      <ResPreview />
      <ResPreview />
      <ResPreview />
      <ResPreview />
      <ResPreview />
      <ResPreview />
      <ResPreview />
      <ResPreview />
      <ResPreview />
      <ResPreview />
      <ResPreview />
      <ResPreview />
      <ResPreview />
      <ResPreview />
    </Flex>
  );
};

export default Index;

Index.getInitialProps = async () => {
  return {
    layoutType: "user",
  };
};
