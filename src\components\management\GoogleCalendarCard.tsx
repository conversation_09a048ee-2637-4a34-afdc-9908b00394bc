import { Button, Flex, Grid, Text, Image } from "@chakra-ui/react";
import React from "react";

type ComponentProp = {
  email?: string;
  disabled?: boolean;
};

export const GoogleCalendarCard = ({ email, disabled }: ComponentProp) => {
  return (
    <Grid
      templateColumns={"1fr 6rem"}
      p={"1.3rem 1.5rem"}
      bg={"chatBackground"}
      borderRadius={"lg"}
    >
      <Flex alignItems={"center"}>
        <Image
          src={"/images/Google_Calendar_icon_2020.svg.png"}
          alt="Google Calendar"
          w={"auto"}
          h={"3rem"}
        />
        <Flex flexDirection={"column"} gap={".3rem"}>
          <Text ml={"1rem"} fontSize={"1.3rem"} fontWeight={"bold"}>
            Google Calendar
          </Text>
          {email && (
            <Text ml={"1rem"} fontSize={"sm"}>
              {email}
            </Text>
          )}
        </Flex>
      </Flex>
      <Flex justifyContent={"flex-end"}>
        {disabled ? (
          <Button
            py={"1.4rem"}
            bg={"secondary"}
            color={"white"}
            w={"100%"}
            opacity={0.3}
          >
            連動解除
          </Button>
        ) : (
          <Button py={"1.4rem"} bg={"secondary"} color={"white"} w={"100%"}>
            連動
          </Button>
        )}
      </Flex>
    </Grid>
  );
};
