import { Flex, Text, UnorderedList, ListI<PERSON>, Link } from "@chakra-ui/react";
import React from "react";
import { USER_NAVIGATION_LIST } from "@/constants/userNavigation";
import { useRouter } from "next/router";

export const NavUser = () => {
  const router = useRouter();
  const { pathname } = router;
  // const activeItem = USER_NAVIGATION_LIST.find(
  //   (item) => item.href === pathname
  // );

  const activeItem = pathname.startsWith("/user/management")
    ? USER_NAVIGATION_LIST[0]
    : pathname.startsWith("/user/message")
    ? USER_NAVIGATION_LIST[1]
    : pathname.startsWith("/user/sales")
    ? USER_NAVIGATION_LIST[2]
    : null;

  console.log(activeItem);

  return (
    <Flex
      w={"100dvw"}
      position={"fixed"}
      top={"6rem"}
      justifyContent={"center"}
      alignItems={"center"}
      px={"1rem"}
      bg={"white"}
      zIndex={100}
    >
      <Flex w={"100dvw"}>
        {USER_NAVIGATION_LIST.map((item) => (
          <Flex
            key={item.name}
            w={"100%"}
            alignItems={"center"}
            borderBottom={
              activeItem?.name === item.name
                ? "3px solid #D74132"
                : "3px solid white"
            }
            bg={activeItem?.name === item.name ? "gray.50" : "white"}
          >
            <Link
              color={"black"}
              fontWeight={activeItem?.name === item.name ? "bold" : "normal"}
              href={item.href}
              w={"100%"}
              py={"1rem"}
              textDecoration={"none"}
              textAlign={"center"}
              fontSize={"1.2rem"}
            >
              {item.name}
            </Link>
          </Flex>
        ))}
      </Flex>
    </Flex>
  );
};
