import { CalendarNav } from "@/components/management/CalendarNav";
import { GoogleCalendarCard } from "@/components/management/GoogleCalendarCard";
import { Flex, Select } from "@chakra-ui/react";
import React from "react";
// import { Calendar, dateFnsLocalizer } from "react-big-calendar";
// import { format, parse, startOfWeek, getDay } from "date-fns";
// import { ja } from "date-fns/locale";
// import "react-big-calendar/lib/css/react-big-calendar.css";

// Type assertion for Calendar component to fix TypeScript compatibility
// const BigCalendar = Calendar as React.ComponentType<any>;

// // Set up the localizer for react-big-calendar
// const localizer = dateFnsLocalizer({
//   format,
//   parse,
//   startOfWeek,
//   getDay,
//   locales: {
//     ja: ja,
//   },
// });

// // Sample events for the calendar
// const events = [
//   {
//     id: 1,
//     title: "予約確認",
//     start: new Date(2024, 7, 15, 10, 0), // August 15, 2024, 10:00 AM
//     end: new Date(2024, 7, 15, 11, 0), // August 15, 2024, 11:00 AM
//   },
//   {
//     id: 2,
//     title: "チェックイン",
//     start: new Date(2024, 7, 16, 15, 0), // August 16, 2024, 3:00 PM
//     end: new Date(2024, 7, 16, 16, 0), // August 16, 2024, 4:00 PM
//   },
// ];

const Index = () => {
  return (
    <Flex flexDirection={"column"} gap={"1rem"}>
      <Select placeholder="好立地、清潔なレジデンシャルステージ" size="lg">
        <option value="option1">
          好立地、清潔なレジデンシャルステージ, AsahiSta...
        </option>
        <option value="option2">
          好立地、清潔なレジデンシャルステージ, AsahiSta...
        </option>
        <option value="option3">
          好立地、清潔なレジデンシャルステージ, AsahiSta...
        </option>
      </Select>
      <GoogleCalendarCard disabled email="<EMAIL>" />
      <GoogleCalendarCard />

      <CalendarNav />

      {/* <BigCalendar
        localizer={localizer}
        events={events}
        startAccessor="start"
        endAccessor="end"
        style={{ height: 500 }}
        culture="ja"
      /> */}
    </Flex>
  );
};

export default Index;

Index.getInitialProps = async () => {
  return {
    layoutType: "user",
  };
};
