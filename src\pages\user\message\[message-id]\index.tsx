import PhoneBookIcon from "@/assets/icons/PhoneBookIcon";
import SendIcon from "@/assets/icons/SendIcon";
import IncomingChatHead from "@/components/message/IncomingChatHead";
import SentChatHead from "@/components/message/SentChatHead";
import {
  Box,
  Button,
  Flex,
  Grid,
  Image,
  Text,
  Textarea,
} from "@chakra-ui/react";
import React from "react";
import { useRouter } from "next/router";

const Index = () => {
  const router = useRouter();

  return (
    <Flex flexDirection={"column"}>
      <Grid
        gap={"1rem"}
        alignItems={"center"}
        pb={"1rem"}
        borderBottom={"1px solid"}
        borderColor={"gray.200"}
        gridTemplateColumns={"5rem 1fr 5rem"}
      >
        <Flex
          overflow={"hidden"}
          alignItems={"center"}
          justifyContent={"center"}
          objectFit={"cover"}
          borderRadius={"1rem"}
          w={"5rem"}
          h={"5rem"}
        >
          <Image
            src={"/images/Rectangle-1.jpg"}
            alt="Tokyo Guesthouse"
            w={"auto"}
            h={"5rem"}
          />
        </Flex>
        <Flex flexDirection={"column"} gap={".4rem"}>
          <Text fontWeight={"bold"} fontSize={"1.2rem"}>
            田中 太郎
          </Text>
          <Text fontSize={".9rem"} color={"gray.500"}>
            好立地、清潔なレジデンシャルステージ, AsahiStay...
          </Text>
        </Flex>
        <Flex justifyContent={"flex-end"}>
          <Button
            variant={"outline"}
            p={0}
            w={"3.5rem"}
            onClick={() => {
              router.push("/user/message/message-detail-info/1");
            }}
          >
            詳細
          </Button>
        </Flex>
      </Grid>
      <IncomingChatHead />
      <SentChatHead />
      <IncomingChatHead />

      <Flex position={"relative"}>
        <Textarea
          placeholder="メッセージを入力してください…"
          size="lg"
          borderRadius={"lg"}
          p={"1rem"}
          mt={"auto"}
          mb={"1rem"}
          h={"6rem"}
          fontSize={".85rem"}
        />
        <Box
          position={"absolute"}
          left={"1rem"}
          bottom={"2rem"}
          cursor={"pointer"}
        >
          <PhoneBookIcon />
        </Box>
        <Box
          position={"absolute"}
          right={"1rem"}
          bottom={"2rem"}
          cursor={"pointer"}
        >
          <SendIcon />
        </Box>
      </Flex>
    </Flex>
  );
};

export default Index;

Index.getInitialProps = async () => {
  return {
    layoutType: "user",
  };
};
